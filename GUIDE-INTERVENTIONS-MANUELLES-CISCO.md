# 🌅🌙 GUIDE D'INTERVENTIONS MANUELLES - SOLEIL & LUNE

**📅 C<PERSON>é le:** 15/08/2025  
**🎯 Objectif:** Guide complet pour contrôler manuellement le soleil et la lune  
**👨‍💻 Destinataire:** <PERSON><PERSON> (contrôles de développement)  
**🔧 Version:** 1.0

---

## 🚀 ACCÈS RAPIDE - CONSOLE DÉVELOPPEUR

### 🌞 CONTRÔLES SOLEIL

```javascript
// Accéder au composant soleil
const sunComponent = document.querySelector('[data-sun-component]');

// Méthodes disponibles (via ref)
// Note: Remplacer 'sunRef' par la référence réelle dans votre code

// 🌅 Phases individuelles
sunRef.current.triggerSunrise();    // Lever de soleil
sunRef.current.triggerMidday();     // Midi (zénith)
sunRef.current.triggerSunset();     // Coucher de soleil
sunRef.current.triggerNight();      // Nuit (soleil caché)

// 🔄 Cycle automatique
sunRef.current.startAutomaticCycle(120000); // 2 minutes par phase
sunRef.current.stopAutomaticCycle();        // Arrêter le cycle

// 🔄 Reset
sunRef.current.resetSun();          // Remettre à l'aube
```

### 🌙 CONTRÔLES LUNE

```javascript
// La lune est contrôlée automatiquement par les phases
// Elle apparaît uniquement en phase 'nuit'

// Pour forcer l'affichage de la lune :
// 1. Changer la phase vers 'nuit'
// 2. La lune apparaîtra automatiquement avec son animation

// Phases supportées :
// - 'aube' / 'dawn'     → Lune cachée
// - 'midi' / 'midday'   → Lune cachée  
// - 'coucher' / 'sunset' → Lune cachée
// - 'nuit' / 'night'    → Lune visible avec animation
```

---

## 🎬 SYSTÈME CINÉMATOGRAPHIQUE

### 📋 PHASES DISPONIBLES

| Phase | Soleil | Lune | Durée Recommandée |
|-------|--------|------|-------------------|
| **🌅 Aube** | Se lève depuis horizon gauche | Cachée | 2 minutes |
| **☀️ Midi** | Au zénith (coin supérieur droit) | Cachée | 2 minutes |
| **🌇 Coucher** | Se couche vers horizon droit | Cachée | 2 minutes |
| **🌙 Nuit** | Complètement caché | Visible avec trajectoire | 2 minutes |

### 🔄 CYCLE AUTOMATIQUE COMPLET

```javascript
// Démarrer un cycle automatique de 8 minutes (2min/phase)
sunRef.current.startAutomaticCycle(120000);

// Cycle personnalisé (exemple: 30 secondes par phase)
sunRef.current.startAutomaticCycle(30000);

// Arrêter le cycle
sunRef.current.stopAutomaticCycle();
```

---

## 🛠️ INTERVENTIONS MANUELLES DÉTAILLÉES

### 🌞 CONTRÔLE PRÉCIS DU SOLEIL

#### 1. **Position Manuelle**
```javascript
// Accéder aux positions prédéfinies
const SUN_POSITIONS = {
  dawn: { angle: -40, horizontalOffset: -90 },    // Sous horizon
  sunrise: { angle: 15, horizontalOffset: -60 },  // Lever
  midday: { angle: 120, horizontalOffset: 85 },   // Zénith
  sunset: { angle: 15, horizontalOffset: 60 },    // Coucher
  night: { angle: -40, horizontalOffset: 90 }     // Caché
};
```

#### 2. **Animation Personnalisée**
```javascript
// Créer une animation personnalisée vers une position
function moveToCustomPosition(angle, horizontalOffset, duration = 5000) {
  const position = angleToPosition(angle, horizontalOffset);
  gsap.to(sunWrapperRef.current, {
    y: position.y,
    x: position.x,
    duration: duration / 1000,
    ease: "power2.inOut"
  });
}

// Exemple : Soleil à 45° avec offset de 20%
moveToCustomPosition(45, 20, 3000);
```

#### 3. **Contrôle du Halo**
```javascript
// Ajuster l'intensité du halo
function setSunGlow(intensity) {
  gsap.to(sunGlowRef.current, {
    opacity: intensity,
    scale: 0.8 + (intensity * 0.6),
    duration: 1
  });
}

// Exemples
setSunGlow(0);    // Pas de halo
setSunGlow(0.5);  // Halo moyen
setSunGlow(1);    // Halo maximum
```

### 🌙 CONTRÔLE PRÉCIS DE LA LUNE

#### 1. **Forcer l'Apparition**
```javascript
// Dans la console, changer la phase vers nuit
// La lune apparaîtra automatiquement

// Si vous avez accès au state de l'app :
setCurrentCinemaPhase('nuit');
```

#### 2. **Trajectoire de la Lune**
La lune suit une trajectoire parabolique prédéfinie :
- **Départ :** Gauche-haut (5vw, 35vh)
- **Zénith :** Centre-haut (50vw, 5vh)  
- **Fin :** Droite-milieu (85vw, 45vh)
- **Durée :** 60 secondes total

#### 3. **Personnalisation Avancée**
```javascript
// Modifier la trajectoire (dans MoonAnimation.tsx)
const customKeyframes = [
  { x: '10vw', y: '40vh', duration: 0 },     // Nouveau départ
  { x: '50vw', y: '10vh', duration: 0.5 },   // Nouveau zénith
  { x: '90vw', y: '50vh', duration: 1 }      // Nouvelle fin
];
```

---

## 🔧 DÉBOGAGE ET DIAGNOSTIC

### 📊 VÉRIFICATIONS RAPIDES

```javascript
// 1. Vérifier l'état actuel
console.log('Phase actuelle:', currentCinemaPhase);

// 2. Vérifier les références
console.log('Soleil ref:', sunriseAnimationRef.current);
console.log('Lune visible:', document.querySelector('[data-moon]'));

// 3. Vérifier les animations GSAP actives
console.log('Animations GSAP:', gsap.globalTimeline.getChildren());
```

### 🚨 RÉSOLUTION DE PROBLÈMES

| Problème | Solution |
|----------|----------|
| **Soleil ne bouge pas** | Vérifier `sunriseAnimationRef.current` non null |
| **Lune n'apparaît pas** | S'assurer que phase = 'nuit' |
| **Animations saccadées** | Réduire la durée ou vérifier les performances |
| **Positions incorrectes** | Vérifier les valeurs dans `SUN_POSITIONS` |

### 🔄 RESET COMPLET

```javascript
// Reset total du système astronomique
function resetAstronomicalSystem() {
  // Arrêter tous les cycles
  sunRef.current?.stopAutomaticCycle();
  
  // Reset soleil
  sunRef.current?.resetSun();
  
  // Reset phase vers aube
  setCurrentCinemaPhase('aube');
  
  console.log('🔄 Système astronomique réinitialisé');
}
```

---

## 📝 NOTES IMPORTANTES

### ⚠️ LIMITATIONS ACTUELLES

1. **Lune :** Pas de contrôle manuel direct (uniquement via phases)
2. **Soleil :** Cycle automatique remplace les contrôles manuels
3. **Performance :** Éviter les animations trop rapides (< 1 seconde)

### 🎯 RECOMMANDATIONS

1. **Durée optimale :** 2 minutes par phase (120000ms)
2. **Ordre naturel :** Aube → Midi → Coucher → Nuit
3. **Tests :** Toujours tester en mode développement d'abord

### 🔮 ÉVOLUTIONS FUTURES

- [ ] Contrôle manuel direct de la lune
- [ ] Positions solaires personnalisables via interface
- [ ] Sauvegarde des configurations personnalisées
- [ ] Mode "time-lapse" accéléré

---

**📞 Support :** Cisco  
**📧 Contact :** Via ContextEngineering/Tasks/Cisco.md  
**🔄 Dernière mise à jour :** 15/08/2025
