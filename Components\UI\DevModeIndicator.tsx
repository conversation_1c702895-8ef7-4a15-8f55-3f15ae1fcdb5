import React from 'react';

// 🔧 CISCO: Indicateur de mode de développement pour identifier la phase actuelle
interface DevModeIndicatorProps {
  currentPhase: string;
  isVisible?: boolean;
}

// 🎨 Configuration des couleurs et emojis par phase
const PHASE_CONFIG = {
  aube: {
    emoji: '🌅',
    name: 'Au<PERSON>',
    color: 'from-orange-500 to-pink-500',
    textColor: 'text-orange-100'
  },
  dawn: {
    emoji: '🌅',
    name: 'Au<PERSON>',
    color: 'from-orange-500 to-pink-500',
    textColor: 'text-orange-100'
  },
  midi: {
    emoji: '☀️',
    name: 'Midi',
    color: 'from-yellow-400 to-orange-400',
    textColor: 'text-yellow-100'
  },
  midday: {
    emoji: '☀️',
    name: 'Midi',
    color: 'from-yellow-400 to-orange-400',
    textColor: 'text-yellow-100'
  },
  coucher: {
    emoji: '🌇',
    name: '<PERSON>ucher',
    color: 'from-red-500 to-purple-500',
    textColor: 'text-red-100'
  },
  sunset: {
    emoji: '🌇',
    name: 'Coucher',
    color: 'from-red-500 to-purple-500',
    textColor: 'text-red-100'
  },
  nuit: {
    emoji: '🌙',
    name: 'Nuit',
    color: 'from-blue-900 to-purple-900',
    textColor: 'text-blue-100'
  },
  night: {
    emoji: '🌙',
    name: 'Nuit',
    color: 'from-blue-900 to-purple-900',
    textColor: 'text-blue-100'
  }
};

const DevModeIndicator: React.FC<DevModeIndicatorProps> = ({ 
  currentPhase, 
  isVisible = true 
}) => {
  // 🔧 CISCO: Masquer en production ou si explicitement désactivé
  if (!isVisible || process.env.NODE_ENV === 'production') {
    return null;
  }

  // 🎨 Récupérer la configuration de la phase actuelle
  const phaseConfig = PHASE_CONFIG[currentPhase as keyof typeof PHASE_CONFIG] || {
    emoji: '❓',
    name: 'Inconnu',
    color: 'from-gray-500 to-gray-600',
    textColor: 'text-gray-100'
  };

  return (
    <div 
      className="fixed top-4 left-4 z-[100] pointer-events-none"
      style={{
        // 🔧 CISCO: Z-index très élevé pour passer au-dessus de tout
        zIndex: 100
      }}
    >
      <div className={`
        bg-gradient-to-r ${phaseConfig.color} 
        backdrop-blur-sm rounded-lg px-3 py-2 
        border border-white/20 shadow-lg
        transform transition-all duration-300 ease-in-out
        hover:scale-105
      `}>
        <div className="flex items-center gap-2">
          <span className="text-lg">{phaseConfig.emoji}</span>
          <div className="flex flex-col">
            <span className={`text-sm font-bold ${phaseConfig.textColor}`}>
              MODE DEV
            </span>
            <span className={`text-xs ${phaseConfig.textColor} opacity-90`}>
              {phaseConfig.name} ({currentPhase})
            </span>
          </div>
        </div>
      </div>
      
      {/* 🔧 CISCO: Indicateur clignotant pour attirer l'attention */}
      <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
    </div>
  );
};

export default DevModeIndicator;
