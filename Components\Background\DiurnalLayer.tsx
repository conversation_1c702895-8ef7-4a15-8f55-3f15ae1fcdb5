import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

// 🔧 CISCO: Interface pour un nuage - NOUVELLE COLLECTION HAUTE QUALITÉ
interface Cloud {
  id: number;
  x: number; // Position X initiale (pour être visible immédiatement)
  y: number; // Position Y aléatoire
  size: number; // Taille du nuage
  duration: number; // Durée de l'animation (vitesse)
  cloudNumber: number; // Numéro du nuage dans la nouvelle collection (48-70)
  verticalDrift: number; // Dérive verticale naturelle
  opacity: number; // Opacité variable
  zIndex: number; // Profondeur pour effet de parallaxe
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  skyMode: string;
}

// 🌤️ CISCO: SYSTÈME FILTRES CSS COMPLET - 8 PHASES AVEC LUMINOSITÉ VARIABLE
// Luminosité selon position solaire + couleurs selon moment de la journée
const getCloudFilterForMode = (mode: string): string => {
  switch (mode) {
    case 'night':
      // 🌌 PHASE 1 : NUIT - Nuages très sombres, presque invisibles
      return 'brightness(0.4) saturate(0.8) contrast(1.0) hue-rotate(0deg)';

    case 'dawn':
      // 🌅 PHASE 2 : AUBE - Plus sombres (suite de la nuit), légèrement rosés
      return 'brightness(0.7) saturate(1.2) contrast(1.2) hue-rotate(8deg) sepia(0.1)';

    case 'sunrise':
      // 🌄 PHASE 3 : LEVER - Légèrement éclairés, teintes dorées
      return 'brightness(0.9) saturate(1.4) contrast(1.1) hue-rotate(15deg) sepia(0.2)';

    case 'midday':
      // ☀️ MIDI - Blancs éclatants (luminosité maximum)
      return 'brightness(1.2) saturate(0.9) contrast(0.95) hue-rotate(0deg)';

    case 'sunset':
      // 🌇 COUCHER - Couleurs douces et pastel (RÉDUCTION sepia et hue-rotate)
      return 'brightness(1.0) saturate(1.2) contrast(1.0) hue-rotate(8deg) sepia(0.1)';

    // 🔧 CISCO: MAPPING DES ANCIENS MODES vers les nouveaux modes cinématographiques
    case 'morning':
    case 'afternoon':
      // Mapper vers midday
      return 'brightness(1.2) saturate(0.9) contrast(0.95) hue-rotate(0deg)';

    case 'dusk':
      // Mapper vers sunset avec couleurs douces
      return 'brightness(1.0) saturate(1.2) contrast(1.0) hue-rotate(8deg) sepia(0.1)';

    default:
      // 🔧 CISCO: Nuages normaux par défaut
      return 'brightness(1.0) saturate(1.0) contrast(1.0)';
  }
};


const DiurnalLayer: React.FC<DiurnalLayerProps> = ({ skyMode }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // 🔧 CISCO: Fonction pour générer les nuages - NOUVELLE COLLECTION HAUTE QUALITÉ
  const generateClouds = (currentSkyMode: string): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 20; // 🔧 CISCO: Optimisation - 20 nuages haute qualité (collection complète)

    // 🔧 CISCO: Collection corrigée - Nuages existants uniquement (48, 50-70)
    const availableCloudNumbers = [48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70]; // 22 nuages disponibles

    // 🔧 CISCO: CORRECTION CRITIQUE - Copie de la liste pour éviter les duplications
    const remainingCloudNumbers = [...availableCloudNumbers];

    for (let i = 0; i < cloudCount; i++) {
      // 🔧 CISCO: CORRECTION - Sélection UNIQUE sans duplication
      if (remainingCloudNumbers.length === 0) {
        console.warn('⚠️ Plus de nuages uniques disponibles, arrêt de la génération');
        break;
      }

      const randomIndex = Math.floor(Math.random() * remainingCloudNumbers.length);
      const cloudNumber = remainingCloudNumbers[randomIndex];

      // 🔧 CISCO: RETIRER le nuage sélectionné pour éviter les duplications
      remainingCloudNumbers.splice(randomIndex, 1);

      console.log(`☁️ Nuage unique sélectionné: cloud_${cloudNumber}.png (${remainingCloudNumbers.length} restants)`);

      // 🔧 CISCO: Tailles optimisées pour nuages haute qualité
      const sizeCategory = Math.random();
      let cloudSize;
      if (sizeCategory < 0.6) {
        // 60% de nuages moyens
        cloudSize = 1.2 + Math.random() * 0.8; // 1.2x à 2.0x
      } else {
        // 40% de grands nuages
        cloudSize = 2.0 + Math.random() * 1.5; // 2.0x à 3.5x
      }

      // 🔧 CISCO: Vitesse UNIFORMISÉE et RALENTIE pour mouvement naturel et contemplatif
      const duration = 1500; // 🔧 CISCO: Vitesse RALENTIE de 1500 secondes (25 minutes) - IDENTIQUE pour tous les nuages

      // 🔧 PHYSIQUE: Dérive verticale ultra variée
      const verticalDrift = (Math.random() - 0.5) * 30; // ±15% de dérive verticale

      // 🔧 CISCO: Opacité fixée à 100% - AUCUN nuage semi-transparent
      const opacity = 1.0; // 100% opaque - TOUJOURS visible

      // 🔧 CISCO: Profondeur selon hiérarchie - Nuages devant le paysage en mode Aube
      const zIndex = currentSkyMode === 'dawn' ? 11 : 9; // Mode Aube: devant paysage (z-11), autres modes: derrière (z-9)

      clouds.push({
        id: i,
        x: -30 - Math.random() * 20, // 🔧 CISCO: TOUS les nuages commencent hors écran à GAUCHE (-30% à -50%) - pas d'apparition "par enchantement"
        y: 3 + Math.random() * 47, // 🔧 CISCO: Position Y uniquement dans la moitié HAUTE (3% à 50%) - pas besoin en bas à cause du paysage
        size: cloudSize,
        duration: duration,
        cloudNumber: cloudNumber, // 🔧 CISCO: Nouveau - numéro du nuage dans la collection
        verticalDrift: verticalDrift,
        opacity: opacity,
        zIndex: zIndex
      });
    }

    // Triple mélange pour dispersion parfaite
    for (let shuffle = 0; shuffle < 3; shuffle++) {
      for (let i = clouds.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [clouds[i], clouds[j]] = [clouds[j], clouds[i]];
      }
    }

    // 🔧 CISCO: VÉRIFICATION - Aucune duplication de nuages
    const cloudNumbers = clouds.map(cloud => cloud.cloudNumber);
    const uniqueCloudNumbers = [...new Set(cloudNumbers)];

    // Vérification silencieuse des duplications
    if (cloudNumbers.length !== uniqueCloudNumbers.length) {
      console.error(`❌ DUPLICATION NUAGES DÉTECTÉE! ${cloudNumbers.length - uniqueCloudNumbers.length} doublons`);
    }

    return clouds;
  };

  // Initialiser les nuages - VERSION SIMPLIFIÉE
  useEffect(() => {
    if (!containerRef.current) return;

    // CISCO: Éviter la duplication - vérifier si déjà initialisé
    if (containerRef.current.children.length > 0) {
      return;
    }

    // Génération des nuages uniques
    const clouds = generateClouds(skyMode);

    // Nettoyer le conteneur existant
    containerRef.current.innerHTML = '';

    clouds.forEach((cloud) => {
      // 🔧 CISCO: Nouvelle collection haute qualité - Sélection par numéro
      const imageSrc = `/Clouds/cloud_${cloud.cloudNumber}.png`;

      // Chargement nuage haute qualité

      // 🔧 CISCO: Chargement robuste avec vérification préalable
      const img = new Image();

      img.onload = () => {
        // Image chargée avec succès, créer l'élément nuage
        const cloudElement = document.createElement('div');
        cloudElement.className = 'cloud';
        cloudElement.setAttribute('data-cloud-element', 'true');

        const animationOffset = Math.random();
        const initialFilter = getCloudFilterForMode(skyMode);

        cloudElement.style.cssText = `
          position: absolute;
          left: ${cloud.x}%;
          top: ${cloud.y}%;
          --cloud-scale: ${cloud.size};
          --vertical-drift: ${cloud.verticalDrift}%;
          --start-x: ${cloud.x - 50}vw;
          --animation-offset: ${animationOffset};
          pointer-events: none;
          z-index: ${cloud.zIndex};
          transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale));
          animation: cloud-drift-realistic ${cloud.duration}s linear infinite;
          animation-delay: ${-cloud.duration * animationOffset}s;
          opacity: ${cloud.opacity};
          will-change: transform;
        `;



        // 🔧 CISCO: IMAGE DIRECTE - FINI LES OVERLAYS RECTANGULAIRES !
        const imgElement = document.createElement('img');
        imgElement.src = imageSrc;
        imgElement.alt = `Nuage ${cloud.cloudNumber}`;
        imgElement.className = 'cloud-image';

        const imageSize = Math.floor(80 + cloud.size * 50);
        imgElement.style.cssText = `
          width: ${imageSize}px;
          height: auto;
          user-select: none;
          transform: translateZ(0);
          backface-visibility: hidden;
          filter: ${initialFilter};
          transition: filter 15s ease-in-out;
        `;

        cloudElement.appendChild(imgElement);
        containerRef.current?.appendChild(cloudElement);
        // Nuage chargé avec succès
      };

      img.onerror = () => {
        console.error(`❌ Erreur chargement nuage: ${imageSrc}`);
      };

      img.src = imageSrc;
    });

    // Ajouter l'animation CSS avec physique réaliste
    if (!document.querySelector('#cloud-animation-style')) {
      const style = document.createElement('style');
      style.id = 'cloud-animation-style';
      style.textContent = `
        @keyframes cloud-drift-realistic {
          0% {
            transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale)) translateX(var(--start-x)) translateY(0);
          }
          20% {
            transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale)) translateX(calc(var(--start-x) + 40vw)) translateY(calc(var(--vertical-drift) * 0.2));
          }
          40% {
            transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale)) translateX(calc(var(--start-x) + 70vw)) translateY(calc(var(--vertical-drift) * 0.5));
          }
          60% {
            transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale)) translateX(calc(var(--start-x) + 100vw)) translateY(calc(var(--vertical-drift) * 0.7));
          }
          80% {
            transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale)) translateX(calc(var(--start-x) + 130vw)) translateY(calc(var(--vertical-drift) * 0.9));
          }
          100% {
            transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale)) translateX(calc(var(--start-x) + 170vw)) translateY(var(--vertical-drift));
          }
        }
        
        .cloud {
          will-change: transform;
        }

        /* Animation alternative pour nuages plus lents (effet de profondeur) */
        @keyframes cloud-drift-slow {
          from {
            transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale)) translateX(-40vw);
          }
          to {
            transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale)) translateX(150vw);
          }
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      const style = document.querySelector('#cloud-animation-style');
      if (style && style.parentNode) {
        style.parentNode.removeChild(style);
      }
    };
  }, []);

  // 🔧 CISCO: SYSTÈME FILTRES CSS SIMPLIFIÉ - Fini les rectangles !
  useEffect(() => {
      if (!containerRef.current) return;

      const cloudFilter = getCloudFilterForMode(skyMode);
      const cloudImages = containerRef.current.querySelectorAll('.cloud-image') as NodeListOf<HTMLImageElement>;

      console.log(`🌤️ Application filtre couleur pour le mode ${skyMode}: ${cloudFilter}`);

      cloudImages.forEach((imgElement) => {
          // 🔧 CISCO: Animation GSAP du filtre CSS - Propre et sans rectangles
          gsap.to(imgElement, {
              filter: cloudFilter,
              duration: 15.0, // Durée synchronisée avec le fond
              ease: "power1.inOut",
              overwrite: true // Écraser toute animation conflictuelle
          });
      });
  }, [skyMode]);

  // 🔧 CISCO: CORRECTION CRITIQUE - Plus de régénération forcée !
  // Les nuages restent les mêmes tout au long du cycle, seuls les filtres changent
  // Cette section a été supprimée selon les instructions de Cisco

  return (
    <div
      ref={containerRef}
      data-diurnal-layer="true"
      className="fixed inset-0 pointer-events-none overflow-hidden"
      style={{
        zIndex: 9 // 🔧 CISCO: Nuages derrière le paysage (z-index 9)
      }}
    />
  );
};

export default DiurnalLayer;
