
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**
# ContextEngineering\Tasks\Cisco.md
# ContextEngineering\TechnicalNotes\journal-technique.md


# Avec mon approbation écrite, tu peux commencer à travailler sur la tâche.
# ContextEngineering\Export\GUIDE-INTERVENTIONS-MANUELLES-CISCO.md

- 


**Attention, il va falloir revoir tout le dégradé du mode midi. Le début est une catastrophe. On a complètement perdu la couleur bleue, ve<PERSON><PERSON><PERSON> vérifier, mais moi c'est flagrant, je le vois à l'écran, c'est une espèce de blanc cassé, on n'a plus de couleur du tout. !!**


-  
- 

- Attention, je confirme, la lune ne se lève pas du tout en mode début de nuit. Donc ce que nous allons faire, c'est en mode coucher du soleil. La lune apparaît légèrement et en fait, quand le mode nuit démarre, ça récupère avec un trigger la position exacte du mode précédent où était la lune, puis le mode nuit la récupère. 

- 


- Attention, rappel, certains dossiers ne doivent pas être commités sur GitHub, comme le context engineering et certains fichiers sensibles, comme par exemple s'il y a des clés, attention, il ne faut pas qu'elles soient visibles.


- Il faut que je vous le dise en japonais. Ça fait depuis tout à l'heure que je vous dis le soleil, il sort de l'écran en haut. Je vous ai dit de le baisser et pas de le monter. !!!!!!!!!!!!!!!!!



































































