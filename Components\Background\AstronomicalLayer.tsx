import React, { useEffect, useRef } from 'react';
import NewStars from './NewStars'; // 🔧 CISCO: Nouveau composant étoiles simplifié
import MoonAnimation from '../UI/MoonAnimation';
import SunriseAnimation, { SunriseAnimationRef } from './SunriseAnimation'; // 🔧 CISCO: Soleil intégré
// 🎬 CISCO: SUPPRESSION useDayCycleOptional - Remplacé par props directes du système Cinema

// 🎬 CISCO: Interface cinématographique simplifiée - 4 modes uniquement
interface AstronomicalLayerProps {
  // Mode du ciel cinématographique pour contrôler la visibilité des étoiles
  skyMode?: 'night' | 'dawn' | 'midday' | 'sunset';
}

const AstronomicalLayer: React.FC<AstronomicalLayerProps> = ({ skyMode = 'night' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sunriseAnimationRef = useRef<SunriseAnimationRef>(null); // 🔧 CISCO: Référence pour le soleil

  // 🎬 CISCO: SYSTÈME CINÉMATOGRAPHIQUE SIMPLIFIÉ - Props directes uniquement
  const currentPhase = skyMode;
  const isTimerRunning = false; // 🎬 CISCO: Plus de timer complexe, géré par CinemaController








  // 🔧 CISCO: Exposition initiale des fonctions du soleil au montage
  useEffect(() => {
    const exposeFunction = () => {
      if (sunriseAnimationRef.current) {
        (window as any).triggerSunriseAnimation = sunriseAnimationRef.current.triggerSunrise;
        (window as any).triggerMorningAnimation = sunriseAnimationRef.current.triggerMorning;
        (window as any).triggerMiddayAnimation = sunriseAnimationRef.current.triggerMidday;
        (window as any).triggerAfternoonAnimation = sunriseAnimationRef.current.triggerAfternoon;
        (window as any).triggerSunsetAnimation = sunriseAnimationRef.current.triggerSunset;
        (window as any).triggerDawnAnimation = sunriseAnimationRef.current.triggerDawn;
        (window as any).triggerDuskAnimation = sunriseAnimationRef.current.triggerDusk;
        (window as any).triggerNightSunAnimation = sunriseAnimationRef.current.triggerNight;
        console.log('☀️ Fonctions soleil exposées globalement depuis AstronomicalLayer');
        return true;
      }
      return false;
    };

    // Essayer d'exposer immédiatement, sinon réessayer
    if (!exposeFunction()) {
      const interval = setInterval(() => {
        if (exposeFunction()) {
          clearInterval(interval);
          // 🔧 CISCO: Déclencher automatiquement le soleil à l'aube au démarrage - SYNCHRONISÉ
          setTimeout(() => {
            if (sunriseAnimationRef.current) {
              console.log('☀️ Déclenchement automatique du soleil à l\'aube (dawn) au démarrage');
              sunriseAnimationRef.current.triggerDawn();
            }
          }, 200);
        }
      }, 100);

      // Nettoyer l'interval après 5s max
      setTimeout(() => clearInterval(interval), 5000);
    } else {
      // 🔧 CISCO: Si exposition immédiate réussie, déclencher le soleil à l'aube - SYNCHRONISÉ
      setTimeout(() => {
        if (sunriseAnimationRef.current) {
          console.log('☀️ Déclenchement automatique du soleil à l\'aube (dawn) au démarrage');
          sunriseAnimationRef.current.triggerDawn();
        }
      }, 200);
    }
  }, []); // 🔧 CISCO: Une seule fois au montage

  // 🔧 CISCO: CONTRÔLE AUTOMATIQUE DU SOLEIL PAR LE SYSTÈME CINÉMATOGRAPHIQUE - 4 PHASES
  useEffect(() => {
    // Déclencher l'animation selon la phase cinématographique
    if (sunriseAnimationRef.current) {
      console.log(`☀️ SYSTÈME CINÉMATOGRAPHIQUE - Déclenchement animation soleil pour phase: ${currentPhase}`);

      switch (currentPhase) {
        case 'nuit':
        case 'night':
          // 🌙 NUIT: Soleil complètement caché, lune visible
          sunriseAnimationRef.current.triggerNight();
          break;
        case 'aube':
        case 'dawn':
          // 🌅 AUBE: SÉQUENCE RÉALISTE - Lune descend → Soleil se lève
          console.log('🌅 AUBE: Séquence réaliste - Lune descend puis soleil se lève');
          sunriseAnimationRef.current.triggerDawn(); // Soleil invisible au début

          // 🔧 CISCO: ASTUCE RÉALISME - Soleil se lève juste avant la fin de l'aube (7 minutes)
          setTimeout(() => {
            console.log('🌅 AUBE: Lever du soleil juste avant la fin de l\'aube (ASTUCE RÉALISME)');
            sunriseAnimationRef.current.triggerSunrise();
          }, 420000); // 7 minutes = juste avant la fin de l'aube

          // 🔧 CISCO: Continuation progressive vers morning pour continuité avec midi
          setTimeout(() => {
            console.log('🌅 AUBE: Progression continue vers position finale pour continuité midi');
            sunriseAnimationRef.current.triggerMorning();
          }, 450000); // 7.5 minutes - progression continue
          break;
        case 'midi':
        case 'midday':
          // ☀️ MIDI: Soleil au zénith (coin supérieur droit)
          sunriseAnimationRef.current.triggerMidday();
          break;
        case 'coucher':
        case 'sunset':
          // 🌇 COUCHER: Progression naturelle depuis midi vers horizon
          console.log('🌇 COUCHER: Progression naturelle du soleil depuis midi vers horizon');
          sunriseAnimationRef.current.triggerSunset(); // Progression directe midi → horizon
          // 🔧 CISCO: Disparition progressive vers dusk après 15 secondes
          setTimeout(() => sunriseAnimationRef.current.triggerDusk(), 15000);
          break;
        default:
          console.warn(`⚠️ Phase cinématographique inconnue: ${currentPhase}`);
          // Fallback vers aube
          sunriseAnimationRef.current.triggerSunrise();
          break;
      }
    }
  }, [currentPhase]);

  // 🔧 CISCO: Suppression du double système d'étoiles - NewStars s'occupe de tout
  useEffect(() => {
    console.log(`🌌 AstronomicalLayer: Mode ${currentPhase} - Délégation à NewStars`);
  }, [currentPhase]);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 pointer-events-none"
      style={{
        zIndex: 8 // 🔧 CISCO: Couche astronomique (étoiles z-7 + lune z-8) - VERROUILLÉ
      }}
    >
      {/* 🌟 CISCO: Étoiles contrôlées par le temporisateur - MAÎTRE ABSOLU */}
      <NewStars skyMode={currentPhase} density="high" />

      {/* ☀️ CISCO: Soleil intégré dans la couche astronomique - DERRIÈRE les nuages */}
      <SunriseAnimation
        ref={sunriseAnimationRef}
        isVisible={true}
      />

      {/* 🌙 CISCO: Lune contrôlée par le système cinématographique */}
      <MoonAnimation
        currentPhase={currentPhase}
      />
    </div>
  );
};

export default AstronomicalLayer;
